{"name": "angular-mod-updater", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^15.2.5", "@angular/common": "^15.2.5", "@angular/compiler": "^15.2.5", "@angular/core": "^15.2.5", "@angular/forms": "^15.2.5", "@angular/platform-browser": "^15.2.5", "@angular/platform-browser-dynamic": "^15.2.5", "@angular/router": "^15.2.5", "@angular/service-worker": "^15.2.5", "@vercel/analytics": "^1.2.2", "@vercel/speed-insights": "^1.0.10", "file-saver": "^2.0.5", "js-sha1": "^0.6.0", "jszip": "^3.10.1", "marked": "^4.3.0", "ngx-dropzone": "^3.1.0", "ngx-markdown": "^15.1.2", "ngx-order-pipe": "^2.2.0", "rxjs": "~7.8.0", "sweetalert2": "^11.4.8", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.4", "@angular/cli": "~15.2.4", "@angular/compiler-cli": "^15.2.5", "@types/file-saver": "^2.0.5", "@types/jasmine": "~4.3.0", "@types/marked": "^4.0.8", "autoprefixer": "^10.4.14", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "~4.9.4"}}