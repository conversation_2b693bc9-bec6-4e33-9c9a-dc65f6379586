<button id="triangle"
        class="h-24 w-24 bg-indigo-400 dark:bg-indigo-500 justify-end pr-4 pt-4 group flex relative aspect-square transition-colors"
        [ngClass]="showInfoSection ? 'h-auto w-auto bg-indigo-500' : 'hover:bg-indigo-500 focus:bg-indigo-500 dark:hover:bg-indigo-400 hover:cursor-pointer'"
        (click)="toggleInfoSection()"
        [@openClose]="showInfoSectionTransition ? 'open' : 'closed'"
        (@openClose.done)="animationDone($event)"
        (@openClose.start)="animationStart($event)"
        tabindex="0"
        aria-controls="info-section"
        attr.aria-expanded="{{showInfoSection}}"
        aria-label="Information section toggle button"
        role="button">

  <!-- (i) icon that appears on hover -->
  <svg *ngIf="!showInfoSection"
       [ngClass]="(this.modrinth.rateLimit_Remaining >= this.modrinth.rateLimit_Limit / 2 ? '' : 'hidden')"
       class="w-8 h-8 group-hover:inline text-white rotate-45 transition-opacity"
       xmlns="http://www.w3.org/2000/svg"
       fill="none"
       viewBox="0 0 24 24"
       stroke-width="2"
       stroke="currentColor"
       aria-hidden="true">  <!-- Hides from screen readers, as it is decorative -->
    <path stroke-linecap="round" stroke-linejoin="round"
          d="M12 11.5v5M12 7.51l.01-.011M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>
  </svg>

  <!-- Remaining rate limit information that appears when the rate limit is below 50% -->
  <span *ngIf="!showInfoSection && this.modrinth.rateLimit_Remaining < this.modrinth.rateLimit_Limit / 2"
        [ngClass]="300 - this.modrinth.rateLimit_Remaining > this.modrinth.rateLimit_Limit - 50 ? 'animate-pulse' : ''"
        class="w-20 text-center opacity-100 group-hover:opacity-0 group-hover:hidden text-white rotate-45 -ml-7"
        aria-live="polite"
  role="status">
  {{ this.modrinth.rateLimit_Limit - Math.max(this.modrinth.rateLimit_Remaining, 0) }}
  / {{ this.modrinth.rateLimit_Limit }}
    <span class="sr-only">tokens remaining.</span>
  </span>
</button>


<app-info-section id="info-section" *ngIf="showInfoSection" (closeEvent)="toggleInfoSection()"></app-info-section>

