<div class="inset-0 fixed z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true" *ngIf="show">
  <div class="flex items-center justify-center pt-4 px-4 min-h-screen text-center sm:block sm:p-0" @fadeInOutAnimation>
    <div class="fixed inset-0" aria-hidden="true"></div>
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

    <div
      class="relative inline-block align-bottom bg-white dark:bg-slate-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:align-middle sm:max-w-[80%] xl:max-w-7xl sm:p-6">
      <div class="absolute top-0 right-0 pt-1 pr-1 mx-4 my-5">
        <button (click)="closeInfoSection()" type="button"
                class="rounded-md text-gray-400 hover:text-gray-500 focus:text-gray-800 dark:text-slate-200 dark:hover:text-slate-100 dark:focus:text-white">
          <span class="sr-only">Close</span>
          <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
               aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <div #modal class="sm:flex mx-4 my-5 sm:items-start  max-h-[75vh] overflow-y-auto" @transformInOutAnimation>
        <div class="info-section">
          <h2 class="font-semibold text-lg">Minecraft Mod Updater and Loader Migrator</h2>
          <p>
            This tool helps you find updates for and lets you migrate Minecraft Java Mods using the
            <a target="_blank" referrerpolicy="no-referrer" href="https://modrinth.com/">Modrinth</a> API by simply
            uploading them as .jar files.
          </p>

          <h3 class="font-semibold text-lg">How to use:</h3>
          <ol>
            <li>Select the desired mod loader (Fabric / Forge / Quilt / NeoForge).</li>
            <li>Select the Minecraft version you want to target.</li>
            <li>Upload your mod(s) by dragging and dropping them onto the page.</li>
            <li>Let the tool find the targeted versions of your mods.</li>
          </ol>
          <p>You can also update resource packs (.zip), shaders (.zip) and modpacks (.mrpack) and downgrade mods to a specific version. <br> Modpack
            developers can use this tool to update the mods contained in their modpacks to the latest versions by
            uploading the modrinth.index.json file.</p>

          <h3 class="font-semibold text-lg">Modrinth's Rate Limit:</h3>
          <p>
            Please note that this tool relies on the Modrinth API for fetching mod updates. Modrinth has a rate limit in
            place to prevent abuse. If you hit the rate limit, you will need to wait for a few seconds. You are able to
            process at most ~290 mods per minute.
            Currently you have used {{ this.modrinth.rateLimit_Limit - Math.max(this.modrinth.rateLimit_Remaining, 0) }}
            of {{ this.modrinth.rateLimit_Limit }}
            tokens{{ this.modrinth.rateLimit_Reset ? ' (resets in ' + this.modrinth.rateLimit_Reset + ' seconds)' : '' }}
            .
          </p>

          <h3 class="font-semibold text-lg">Attributions:</h3>
          <p>
            This tool was made with <a target="_blank" referrerpolicy="no-referrer"
                                       href="https://angular.io/">Angular</a> and <a target="_blank"
                                                                                     referrerpolicy="no-referrer"
                                                                                     href="https://tailwindcss.com/">Tailwind</a>
            using icons from <a target="_blank" referrerpolicy="no-referrer" href="https://heroicons.com/">Heroicons</a>
            and <a target="_blank" referrerpolicy="no-referrer" href="https://iconoir.com/">Iconoir</a>.
            The dropzone was created using the <a target="_blank" referrerpolicy="no-referrer"
                                                  href="https://www.npmjs.com/package/ngx-dropzone">ngx-dropzone
            library</a>.
            The source code is available on <a target="_blank" referrerpolicy="no-referrer"
                                               href="https://github.com/IsAvaible/AngularModUpdater">GitHub</a>.
          </p>

          <label class="inline-flex items-center cursor-pointer" (click)="toggleCurseforgeSupport($event)">
            <input type="checkbox" [(ngModel)]="curseforgeSupport">
            <span class="ml-3 text-sm text-gray-900 dark:text-gray-300">Enable Experimental Curseforge Support</span>
          </label>
        </div>

        <button (click)="closeInfoSection()" type="button"
                class="sm:hidden mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-slate-900 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 dark:bg-slate-800 dark:text-slate-200 hover:dark:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
