<div class="p-4 w-full bg-white dark:bg-slate-800 dark:text-slate-200 rounded-lg shadow-md flex flex-col" *ngIf="view == View.List">
  <div class="overflow-y-auto flex gap-x-5">
  <div class="flex flex-col justify-center sm:order-first min-w-16 min-h-16">
    <a *ngIf="view == View.List" href="https://modrinth.com/mod/{{project.slug}}" target="_blank" referrerpolicy="no-referrer" class="hidden sm:block" title="Open the Projects Modrinth Page"  tabindex="-1">
      <img src="{{project.icon_url}}" alt="{{project.title}}" class="aspect-square w-16 rounded-xl" style="background-color: {{project.color}}"/>
    </a>
  </div>
  <div class="flex flex-col flex-grow order-last sm:order-first">
    <div class="flex flex-row gap-x-1 items-baseline">
      <h2 class="text-lg font-bold dark:text-slate-100"><a class="hover:underline" href="{{project.project_url}}" target="_blank" referrerpolicy="no-referrer">{{project.title}}</a></h2>
      <p>by <a class="underline" href="https://modrinth.com/user/{{selectedVersion.author_id}}" title="Open the Author's Modrinth Page" target="_blank" referrerpolicy="no-referrer">{{selectedVersion.author_id}}</a></p>
    </div>
    <p class="text-gray-700 dark:text-slate-200">{{project.description}}</p>
    <div class="flex flex-row gap-x-3 text-sm">
      <span class="flex flex-wrap items-center gap-x-1">
        <svg *ngIf="project.server_side !== 'unsupported'" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="M2 12h20M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg>
        <svg *ngIf="project.server_side === 'unsupported'" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17 9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2z"></path></svg>
        <span [ngSwitch]="project.server_side">
          <span *ngSwitchCase="'required'">Server</span>
          <span *ngSwitchCase="'optional'">Client or Server</span>
          <span *ngSwitchDefault>Client</span>
        </span>
      </span>
      <button title="Open the Changelog" (click)="toggleChangelog()" class="flex items-center gap-x-1 hover:underline">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" /></svg>
        {{selectedVersion.date_published | dateAgo}}
      </button>
      <span class="flex items-center justify-center gap-x-1 dark:text-slate-200">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="14" height="14">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9" />
        </svg>
        {{selectedVersion.version_type.charAt(0).toUpperCase() + selectedVersion.version_type.slice(1)}}
      </span>
      <app-mod-status-label *ngIf="selectedVersion.versionStatus != VersionStatus.Unspecified" [status]="selectedVersion.versionStatus" [view]="view"/>
      <app-project-type-label [type]="project.project_type" [view]="view"/>
    </div>
  </div>
  <div class="flex flex-col justify-center flex-shrink items-center sm:items-start space-y-1 order-first sm:order-last">
    <a *ngIf="view == View.List" href="https://modrinth.com/mod/{{project.slug}}" target="_blank" referrerpolicy="no-referrer" class="aspect-square sm:hidden" title="Open the Projects Modrinth Page"  tabindex="-1">
      <img src="{{project.icon_url}}" alt="{{project.title}}" class="h-8 w-8 rounded-lg"/>
    </a>

    <div class="flex flex-row gap-x-1 items-center w-full justify-center sm:justify-start">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 text-gray-900 dark:text-slate-200">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
      </svg>
      <p *ngIf="versions.length == 1" class="text-sm text-gray-700 dark:text-slate-200">{{selectedVersion.version_number}}</p>
      <div *ngIf="versions.length > 1" class="">
        <div class="flex flex-row">
          <select [(ngModel)]="selectedVersion" class="text-sm font-medium text-gray-700 dark:text-slate-200 bg-white dark:bg-slate-800 dark:text-slate-200 border border-gray-300 rounded-xl px-2 py-1">
            <option [class]="mod.versionStatus == VersionStatus.Installed ? 'bg-slate-100 dark:bg-slate-700' : ''" *ngFor="let mod of versions" [ngValue]="mod">
              {{mod.version_number}}
            </option>
          </select>
        </div>
      </div>
    </div>
    <p class="text-sm text-gray-700 dark:text-slate-200 flex gap-x-1 sm:w-48">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 text-gray-900 dark:text-slate-200">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 8.25H7.5a2.25 2.25 0 00-2.25 2.25v9a2.25 2.25 0 002.25 2.25h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25H15M9 12l3 3m0 0l3-3m-3 3V2.25" />
      </svg>
      <span class="">{{selectedVersion.downloads | shortNumber}}</span> downloads
    </p>
    <a href="{{selectedVersion.files[0].url}}" class="text-sm font-medium text-blue-500 hover:text-blue-700 hover:underline">Download</a>
  </div>
  </div>
  <div *ngIf="showChangelog" @slideInOut class="pt-2 overflow-y-clip">
    <h4 class="font-bold text-2xl">Changelog:</h4>
    <markdown class="undo-preflight text-sm" [data]="selectedVersion.changelog"></markdown>
    <button (click)="toggleChangelog()" class="w-full h-7 hover:bg-gray-100 dark:bg-slate-800 dark:hover:bg-slate-700 flex justify-center items-center rounded-md">
      <svg [ngClass]="showChangelog ? '' : 'rotate-180'" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
      </svg>
      <span class="sr-only">Toggle Changelog</span>
    </button>
  </div>
</div>

<div class="p-4 w-full h-full items-center bg-white dark:bg-slate-800 dark:text-slate-200 rounded-lg shadow-md overflow-y-auto flex gap-x-5" *ngIf="view === View.Grid">
  <div class="flex flex-col justify-center min-w-16 min-h-16">
    <a href="https://modrinth.com/mod/{{project.slug}}" target="_blank" referrerpolicy="no-referrer" class="" title="{{project.title}}" tabindex="-1">
      <img src="{{project.icon_url}}" alt="{{project.title}}" class="aspect-square w-16 rounded-xl"/>
    </a>
  </div>
  <div class="flex flex-col flex-grow">
    <div class="flex flex-row gap-x-1 items-baseline">
      <h2 class="text-lg font-bold"><a class="hover:underline" href="https://modrinth.com/mod/{{project.slug}}" target="_blank" referrerpolicy="no-referrer">{{project.title}}</a></h2>
    </div>
    <div class="flex flex-row gap-x-1 items-center w-full justify-start">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 text-gray-900 dark:text-slate-200">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
      </svg>
      <p *ngIf="versions.length == 1" class="text-sm text-gray-700 dark:text-slate-200">{{selectedVersion.version_number}}</p>
      <div *ngIf="versions.length > 1" class="">
        <div class="flex flex-row">
          <select [(ngModel)]="selectedVersion" class="text-sm font-medium text-gray-700 dark:text-slate-200 bg-white dark:bg-slate-800 border border-gray-300 rounded-xl px-2 py-1">
            <option [class]="mod.versionStatus == VersionStatus.Installed ? 'bg-slate-100 dark:bg-slate-700' : ''" *ngFor="let mod of versions" [ngValue]="mod">
              {{mod.version_number}}
            </option>
          </select>
        </div>
      </div>

    </div>
    <p class="text-sm text-gray-700 dark:text-slate-200 flex gap-x-1">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 text-gray-900 dark:text-slate-200">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 8.25H7.5a2.25 2.25 0 00-2.25 2.25v9a2.25 2.25 0 002.25 2.25h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25H15M9 12l3 3m0 0l3-3m-3 3V2.25" />
      </svg>
      <span class="">{{selectedVersion.downloads | shortNumber}}</span> downloads
    </p>
    <div class="flex justify-between">
      <a [href]="fileUrl" class="text-sm font-medium text-blue-500 hover:text-blue-700 hover:underline">Download</a>
      <app-mod-status-label [status]="selectedVersion.versionStatus" [view]="view"/>
    </div>

  </div>
</div>
