<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Minecraft Mod Updater</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/apple-touch-icon.png?v=1.0.0">
  <link rel="icon" href="/favicon.svg" type="image/svg+xml">
  <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/favicon-32x32.png?v=1.0.0">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/icons/favicon-16x16.png?v=1.0.0">
  <link rel="mask-icon" href="assets/icons/safari-pinned-tab.svg?v=1.0.0" color="#5bbad5">
  <link rel="shortcut icon" href="/favicon.ico?v=1.0.0">
  <meta name="theme-color" content="#ffffff">
  <link rel="manifest" href="manifest.webmanifest?v=1.0.0">
  <meta name="theme-color" content="#847bf5">
  <meta name="google-site-verification" content="b6vGWQfc8o908ISF9mV-x5YUkhrMq0lf8LHpfil2cEs" />
  <meta name="description" content="An easy-to-use config-free online Minecraft Mod updater and migrator for Fabric, Forge, Quilt and NeoForge. Also supports updating resource packs, shaders, and modpacks, or downgrading mods as needed. Now with optional CurseForge support. Perfect for modpack developers to keep collections current.">
  <meta name="keywords" content="Minecraft, Mod, Updater, Loader, Migrator, Fabric, Forge, Quilt, NeoForge, Modrinth, API, Update, Resource Pack, Shader, Modpack, Downgrade, CurseForge, Easy, Migrate">
  <meta name="page-topic" content="Minecraft Mod Updater">
  <meta name="author" content="Simon Felix Conrad">
</head>
<body class="bg-slate-100 dark:bg-slate-900 dark:text-slate-200">
  <app-root></app-root>
  <noscript>Please enable JavaScript to continue using this application.</noscript>
</body>
</html>
